import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  FlatList,
} from 'react-native';
import { X } from 'lucide-react-native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import Dropdown from '@/components/Dropdown';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
}

interface OfferNegotiationConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (config: OfferNegotiationConfig) => void;
  initialConfig?: OfferNegotiationConfig;
}

const { height: screenHeight } = Dimensions.get('window');
const ITEMS_PER_PAGE = 10;

export default function OfferNegotiationConfigModal({
  visible,
  onClose,
  onSave,
  initialConfig,
}: OfferNegotiationConfigModalProps) {
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  
  const [offerConfig, setOfferConfig] = useState<OfferNegotiationConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
  });

  // Fetch dropdown data
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === offerConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  useEffect(() => {
    if (initialConfig) {
      setOfferConfig(initialConfig);
    } else {
      setOfferConfig({
        search: '',
        rentSale: '',
        towerBuilding: '',
        bedrooms: [],
        minArea: '',
        maxArea: '',
        priceMin: '',
        priceMax: '',
      });
    }
    setCurrentPage(1);
  }, [initialConfig, visible]);

  const updateOfferConfig = (field: keyof OfferNegotiationConfig, value: string | string[] | (string | number)[]) => {
    setOfferConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Create filters object for listings API
  const createListingFilters = (config: OfferNegotiationConfig) => {
    const filters: any = {};

    if (config.search) {
      filters.location = config.search;
    }

    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    return filters;
  };

  // Check if any filters are applied
  const hasFilters = offerConfig?.search || offerConfig?.rentSale || offerConfig?.towerBuilding ||
                    (offerConfig?.bedrooms && offerConfig.bedrooms.length > 0) || offerConfig?.minArea || 
                    offerConfig?.maxArea || offerConfig?.priceMin || offerConfig?.priceMax;

  // Fetch listings with pagination
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-offer-listings', offerConfig, currentPage],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(currentPage, params);
      }
      const filters = createListingFilters(offerConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(currentPage, params);
    },
    enabled: visible,
  });

  const listings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  const resetFilters = () => {
    setOfferConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });
    setCurrentPage(1);
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSave = () => {
    onSave(offerConfig);
    onClose();
  };

  const handleClose = () => {
    setOfferConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });
    setCurrentPage(1);
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Offer Negotiation Configuration</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.configContainer}>
            <Text style={styles.configTitle}>Lead configuration</Text>
            
            {/* First Row: Location and Tower/Building */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Location"
                  options={locationOptions}
                  selectedValue={offerConfig.search}
                  onSelect={(option) => updateOfferConfig('search', option.id.toString())}
                  placeholder="Search location"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Tower/Building"
                  options={finalTowerOptions}
                  selectedValue={offerConfig.towerBuilding}
                  onSelect={(option) => updateOfferConfig('towerBuilding', option.id.toString())}
                  placeholder="Tower/Building"
                  searchable={true}
                  searchPlaceholder="Search towers..."
                />
              </View>
            </View>

            {/* Second Row: Rent/Sale and Min Area */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Rent/Sale"
                  options={RENT_SALE_OPTIONS}
                  selectedValue={offerConfig.rentSale}
                  onSelect={(option) => updateOfferConfig('rentSale', option.id.toString())}
                  placeholder="Select Rent/Sale"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Min Area"
                  options={MIN_AREA_OPTIONS}
                  selectedValue={offerConfig.minArea}
                  onSelect={(option) => updateOfferConfig('minArea', option.id.toString())}
                  placeholder="Select Min Area"
                />
              </View>
            </View>

            {/* Third Row: Max Area and Price Min */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Max Area"
                  options={MAX_AREA_OPTIONS}
                  selectedValue={offerConfig.maxArea}
                  onSelect={(option) => updateOfferConfig('maxArea', option.id.toString())}
                  placeholder="Select Max Area"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Price Min"
                  options={PRICE_MIN_OPTIONS}
                  selectedValue={offerConfig.priceMin}
                  onSelect={(option) => updateOfferConfig('priceMin', option.id.toString())}
                  placeholder="Select Price Min"
                />
              </View>
            </View>

            {/* Fourth Row: Price Max and Bedrooms */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Price Max"
                  options={PRICE_MAX_OPTIONS}
                  selectedValue={offerConfig.priceMax}
                  onSelect={(option) => updateOfferConfig('priceMax', option.id.toString())}
                  placeholder="Select Price Max"
                />
              </View>
              <View style={styles.halfWidth}>
                <MultiSelectDropdown
                  label="Bedrooms"
                  options={bedroomOptions}
                  selectedValues={offerConfig.bedrooms}
                  onSelect={(selectedIds) => updateOfferConfig('bedrooms', selectedIds)}
                  placeholder="Select Bedrooms"
                  showConfirmButton={true}
                  confirmButtonText="OK"
                />
              </View>
            </View>

            {/* Reset filters button */}
            <View style={styles.offerButtonsContainer}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={resetFilters}
              >
                <Text style={styles.resetButtonText}>Reset filters</Text>
              </TouchableOpacity>
            </View>

            {/* Offer Negotiation Listings Results */}
            {isLoadingListings ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>Loading properties...</Text>
              </View>
            ) : listings.length > 0 ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>
                  Properties ({listings.length} din {totalListings})
                </Text>
                <FlatList
                  data={listings}
                  keyExtractor={(item) => item.id.toString()}
                  renderItem={({ item }: { item: any }) => (
                    <View style={styles.listingItem}>
                      <View style={styles.listingCardContainer}>
                        <ListingCard
                          listing={item}
                          disableNavigation={true}
                          compact={true}
                          onPress={() => {
                            console.log('Selected listing for offer negotiation:', item.id);
                          }}
                        />
                      </View>
                    </View>
                  )}
                  style={styles.listingsList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                  ListFooterComponent={() =>
                    totalPages > 1 ? (
                      <View style={styles.paginationContainer}>
                        <Pagination
                          currentPage={currentPage}
                          totalPages={totalPages}
                          onPageChange={handlePageChange}
                        />
                      </View>
                    ) : null
                  }
                />
              </View>
            ) : (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>No properties found</Text>
              </View>
            )}

            {/* Properties table placeholder */}
            <View style={styles.propertiesContainer}>
              <Text style={styles.noItemsText}>No items selected</Text>
            </View>
          </ScrollView>

          {/* Save Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  configContainer: {
    padding: 16,
    maxHeight: screenHeight * 0.7,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  rowContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  offerButtonsContainer: {
    marginVertical: 16,
  },
  resetButton: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  listingsContainer: {
    marginTop: 16,
  },
  listingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  listingsList: {
    maxHeight: 300,
  },
  listingItem: {
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCardContainer: {
    flex: 1,
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  propertiesContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    alignItems: 'center',
  },
  noItemsText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  buttonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
