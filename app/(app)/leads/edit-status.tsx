import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchLeadStatuses } from '@/lib/api';
import MeetingConfigModal from '@/components/MeetingConfigModal';
import ViewingConfigModal from '@/components/ViewingConfigModal';
import FollowUpConfigModal from '@/components/FollowUpConfigModal';
import OfferNegotiationConfigModal from '@/components/OfferNegotiationConfigModal';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

export default function EditStatusScreen() {
  const { leadId, currentStatus } = useLocalSearchParams();
  const [selectedStatus, setSelectedStatus] = useState(currentStatus?.toString() || '');
  const [showMeetingModal, setShowMeetingModal] = useState(false);
  const [showViewingModal, setShowViewingModal] = useState(false);
  const [showFollowUpModal, setShowFollowUpModal] = useState(false);
  const [showOfferNegotiationModal, setShowOfferNegotiationModal] = useState(false);

  // Fetch lead statuses
  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  const enabledStatuses = leadStatuses.filter((status: StatusOption) => status.is_disabled === 0);

  useEffect(() => {
    setSelectedStatus(currentStatus?.toString() || '');
  }, [currentStatus]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusToggle = (statusId: string) => {
    setSelectedStatus(statusId);

    // Check if status requires configuration
    const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
    if (selectedStatusObj) {
      if (selectedStatusObj.name === 'MEETING_SCHEDULED') {
        setShowMeetingModal(true);
      } else if (selectedStatusObj.name === 'VIEWING_SCHEDULED') {
        setShowViewingModal(true);
      } else if (selectedStatusObj.name === 'FOLLOW_UP') {
        setShowFollowUpModal(true);
      } else if (selectedStatusObj.name === 'OFFER_NEGOTIATION') {
        setShowOfferNegotiationModal(true);
      } else {
        // For simple statuses, save immediately and go back
        handleSaveStatus(statusId);
      }
    }
  };

  const handleSaveStatus = (statusId: string, config?: any) => {
    // Here you would typically save the status to the API
    console.log('Saving status:', statusId, 'with config:', config);

    // Navigate back with the selected status
    router.back();
  };

  const handleMeetingConfigSave = (config: MeetingConfig) => {
    handleSaveStatus(selectedStatus, config);
  };

  const handleViewingConfigSave = (config: ViewingConfig) => {
    handleSaveStatus(selectedStatus, config);
  };

  const handleFollowUpConfigSave = (config: FollowUpConfig) => {
    handleSaveStatus(selectedStatus, config);
  };

  const handleOfferNegotiationConfigSave = (config: OfferNegotiationConfig) => {
    handleSaveStatus(selectedStatus, config);
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Status</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Status Pills */}
        <ScrollView
          horizontal
          style={styles.statusScrollContainer}
          contentContainerStyle={styles.statusScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {enabledStatuses.map((status: StatusOption) => (
            <TouchableOpacity
              key={status.id}
              style={[
                styles.statusPill,
                selectedStatus === status.id.toString() && styles.selectedStatusPill
              ]}
              onPress={() => handleStatusToggle(status.id.toString())}
            >
              <Text
                style={[
                  styles.statusPillText,
                  selectedStatus === status.id.toString() && styles.selectedStatusPillText
                ]}
              >
                {formatStatusName(status.name)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Content area for future use */}
        <View style={styles.content}>
          <Text style={styles.instructionText}>
            Select a status above. Some statuses will open additional configuration options.
          </Text>
        </View>
      </SafeAreaView>

      {/* Modal Components */}
      <MeetingConfigModal
        visible={showMeetingModal}
        onClose={() => setShowMeetingModal(false)}
        onSave={handleMeetingConfigSave}
      />

      <ViewingConfigModal
        visible={showViewingModal}
        onClose={() => setShowViewingModal(false)}
        onSave={handleViewingConfigSave}
      />

      <FollowUpConfigModal
        visible={showFollowUpModal}
        onClose={() => setShowFollowUpModal(false)}
        onSave={handleFollowUpConfigSave}
      />

      <OfferNegotiationConfigModal
        visible={showOfferNegotiationModal}
        onClose={() => setShowOfferNegotiationModal(false)}
        onSave={handleOfferNegotiationConfigSave}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  placeholder: {
    width: 40,
  },
  statusScrollContainer: {
    paddingVertical: 16,
  },
  statusScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  statusPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
    marginRight: 8,
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B89C4C',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
